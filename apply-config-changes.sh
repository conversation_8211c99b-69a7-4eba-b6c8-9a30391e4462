#!/bin/bash

# Script to apply environment configuration changes to OpenSearch cluster
# This ensures your CLUSTER_NAME and NODE_NAME changes are properly applied

echo "🔧 Applying OpenSearch Configuration Changes"
echo "=============================================="

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found!"
    exit 1
fi

# Load environment variables
source .env

echo "📋 Updated Configuration from .env:"
echo "  CLUSTER_NAME: ${CLUSTER_NAME}"
echo "  NODE_NAME: ${NODE_NAME}"
echo "  OPENSEARCH_PORT: ${OPENSEARCH_PORT}"
echo ""

# Check current running configuration
echo "🔍 Checking Current Running Configuration:"
if curl -s "http://localhost:${OPENSEARCH_PORT}/" > /dev/null 2>&1; then
    CURRENT_INFO=$(curl -s "http://localhost:${OPENSEARCH_PORT}/")
    CURRENT_CLUSTER=$(echo "$CURRENT_INFO" | grep -o '"cluster_name":"[^"]*"' | cut -d'"' -f4)
    CURRENT_NODE=$(echo "$CURRENT_INFO" | grep -o '"name":"[^"]*"' | cut -d'"' -f4)
    
    echo "  Current Cluster: ${CURRENT_CLUSTER}"
    echo "  Current Node: ${CURRENT_NODE}"
    echo ""
    
    # Check if configuration needs updating
    if [ "$CURRENT_CLUSTER" = "$CLUSTER_NAME" ] && [ "$CURRENT_NODE" = "$NODE_NAME" ]; then
        echo "✅ Configuration is already up to date!"
        echo "   No restart needed."
        exit 0
    else
        echo "⚠️  Configuration mismatch detected!"
        echo "   Expected: ${CLUSTER_NAME}/${NODE_NAME}"
        echo "   Current:  ${CURRENT_CLUSTER}/${CURRENT_NODE}"
        echo "   Restart required to apply changes."
        echo ""
    fi
else
    echo "  OpenSearch is not responding or not running."
    echo ""
fi

# Check if services are running
if docker compose ps | grep -q "opensearch-dev.*Up"; then
    echo "🔄 OpenSearch is currently running. Applying changes requires restart..."
    
    # Graceful shutdown
    echo "⏹️  Stopping OpenSearch services..."
    docker compose down
    
    # Wait a moment for cleanup
    sleep 3
    
    # Start with new configuration
    echo "🚀 Starting OpenSearch with updated configuration..."
    docker compose up -d
    
    # Wait for services to be ready
    echo "⏳ Waiting for OpenSearch to be ready..."
    sleep 10
    
    # Health check with timeout
    echo "🏥 Checking cluster health..."
    for i in {1..30}; do
        if curl -s "http://localhost:${OPENSEARCH_PORT}/_cluster/health" > /dev/null 2>&1; then
            echo "✅ OpenSearch is ready!"
            break
        fi
        echo "   Attempt $i/30: Waiting for OpenSearch..."
        sleep 2
    done
    
    # Verify configuration
    echo ""
    echo "🔍 Verifying Updated Configuration:"
    CLUSTER_INFO=$(curl -s "http://localhost:${OPENSEARCH_PORT}/" 2>/dev/null)
    if [ $? -eq 0 ]; then
        NEW_CLUSTER=$(echo "$CLUSTER_INFO" | grep -o '"cluster_name":"[^"]*"' | cut -d'"' -f4)
        NEW_NODE=$(echo "$CLUSTER_INFO" | grep -o '"name":"[^"]*"' | cut -d'"' -f4)
        
        echo "✅ Cluster accessible"
        echo "   Cluster Name: ${NEW_CLUSTER}"
        echo "   Node Name: ${NEW_NODE}"
        
        # Final verification
        if [ "$NEW_CLUSTER" = "$CLUSTER_NAME" ] && [ "$NEW_NODE" = "$NODE_NAME" ]; then
            echo ""
            echo "🎉 Configuration successfully updated!"
            echo "   ✅ Cluster: ${CLUSTER_NAME}"
            echo "   ✅ Node: ${NODE_NAME}"
        else
            echo ""
            echo "❌ Configuration update failed!"
            echo "   Expected: ${CLUSTER_NAME}/${NODE_NAME}"
            echo "   Actual: ${NEW_CLUSTER}/${NEW_NODE}"
            exit 1
        fi
    else
        echo "❌ Failed to connect to OpenSearch"
        exit 1
    fi
    
else
    echo "💡 OpenSearch is not running. Configuration will be applied on next startup."
    echo "   Run: docker compose up -d"
fi

echo ""
echo "✅ Configuration changes applied successfully!"
echo ""
echo "🌐 Access Points:"
echo "  OpenSearch API: http://localhost:${OPENSEARCH_PORT}"
echo "  OpenSearch Dashboards: http://localhost:${DASHBOARDS_PORT:-5601}"
echo ""
echo "🔧 Verification Commands:"
echo "  curl http://localhost:${OPENSEARCH_PORT}/ | jq"
echo "  curl http://localhost:${OPENSEARCH_PORT}/_cluster/health | jq"
echo "  curl http://localhost:${OPENSEARCH_PORT}/_cat/nodes?v"
