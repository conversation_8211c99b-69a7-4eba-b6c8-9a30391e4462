# CSV to OpenSearch Data Ingestion

This directory contains tools for ingesting CSV data into OpenSearch with optimized performance, error handling, and data transformation capabilities.

## 🚀 Quick Start

### 1. Prerequisites

```bash
# Install Python dependencies
pip install -r requirements.txt

# Start OpenSearch cluster
make up
```

### 2. Basic Usage

```bash
# Ingest the default CSV file
python csv_to_opensearch.py

# Ingest with custom parameters
python csv_to_opensearch.py --csv-file my_data.csv --index-name my-products --batch-size 500

# Recreate index (delete existing data)
python csv_to_opensearch.py --recreate-index
```

### 3. Query the Data

```bash
# Run example queries
python query_examples.py
```

## 📁 Files Overview

| File | Purpose |
|------|---------|
| `csv_to_opensearch.py` | Main ingestion script with full error handling |
| `query_examples.py` | Demonstrates various search patterns |
| `requirements.txt` | Python dependencies |
| `.env` | Configuration (updated with ingestion settings) |

## 🔧 Configuration

The ingestion process uses environment variables from `.env`:

```bash
# OpenSearch Connection
OPENSEARCH_HOST=localhost
OPENSEARCH_PORT=9200

# Data Ingestion Settings
INDEX_NAME=grocery-products
BATCH_SIZE=1000
```

## 📊 Data Transformation

The script automatically:

- **Cleans missing values**: Handles empty brands, thumbnails, etc.
- **Converts data types**: Ensures numeric fields are properly typed
- **Calculates derived fields**:
  - `discount_percentage`: Calculated from MRP and sale price
  - `price_per_gram`: Price efficiency metric
  - `ingestion_timestamp`: When the data was indexed

## 🗂️ Index Mapping

Optimized mapping for grocery product data:

```json
{
  "product_name": "text with keyword field for exact matching",
  "brand": "keyword for filtering",
  "category": "keyword for aggregations",
  "weight": "integer (grams)",
  "sale_price": "float (₹)",
  "mrp": "float (₹)",
  "discount": "float (₹)",
  "quantity": "integer (stock)",
  "source": "keyword (e.g., Instamart)",
  "discount_percentage": "float (calculated)",
  "price_per_gram": "float (calculated)",
  "ingestion_timestamp": "date"
}
```

## 🔍 Query Examples

### Basic Text Search
```python
{
  "query": {
    "match": {
      "product_name": "tomato"
    }
  }
}
```

### Filtered Search
```python
{
  "query": {
    "bool": {
      "must": [{"term": {"category.keyword": "Vegetables"}}],
      "filter": [{"range": {"sale_price": {"gte": 50, "lte": 100}}}]
    }
  }
}
```

### Aggregations
```python
{
  "aggs": {
    "categories": {
      "terms": {"field": "category.keyword"}
    },
    "avg_price": {
      "avg": {"field": "sale_price"}
    }
  }
}
```

## 🛠️ Advanced Usage

### Custom CSV Structure

If your CSV has different columns, modify the `transform_data()` method:

```python
# Add custom transformations
df['custom_field'] = df['existing_field'].apply(lambda x: custom_logic(x))
```

### Error Handling

The script includes comprehensive error handling:

- **Connection failures**: Validates OpenSearch connectivity
- **Data validation**: Checks required columns
- **Bulk indexing errors**: Logs failed documents
- **Retry logic**: Automatic retries with exponential backoff

### Performance Tuning

For large datasets:

```bash
# Increase batch size
python csv_to_opensearch.py --batch-size 2000

# Monitor progress in logs
tail -f opensearch_ingestion.log
```

## 📈 Monitoring

### Index Statistics
```bash
curl "localhost:9200/grocery-products/_stats?pretty"
```

### Document Count
```bash
curl "localhost:9200/grocery-products/_count?pretty"
```

### Sample Documents
```bash
curl "localhost:9200/grocery-products/_search?size=3&pretty"
```

## 🔧 Troubleshooting

### Common Issues

1. **Connection Refused**
   ```bash
   # Check if OpenSearch is running
   make status
   curl localhost:9200
   ```

2. **Memory Issues**
   ```bash
   # Reduce batch size
   python csv_to_opensearch.py --batch-size 100
   ```

3. **Index Already Exists**
   ```bash
   # Recreate index
   python csv_to_opensearch.py --recreate-index
   ```

### Logs

Check detailed logs in:
- `opensearch_ingestion.log` - Ingestion process logs
- `docker compose logs opensearch` - OpenSearch server logs

## 🎯 Next Steps

1. **Set up monitoring**: Use OpenSearch Dashboards for visualization
2. **Create search APIs**: Build REST endpoints for your application
3. **Implement real-time updates**: Set up change data capture
4. **Add security**: Enable authentication for production use

## 📚 Resources

- [OpenSearch Documentation](https://opensearch.org/docs/)
- [OpenSearch Python Client](https://opensearch.org/docs/latest/clients/python/)
- [Bulk API Reference](https://opensearch.org/docs/latest/api-reference/document-apis/bulk/)
