#!/usr/bin/env python3
"""
OpenSearch Query Examples

This script demonstrates various ways to query the grocery products data
that has been indexed into OpenSearch.

Usage:
    python query_examples.py
"""

import os
import json
from dotenv import load_dotenv
from opensearchpy import OpenSearch


def create_client():
    """Create OpenSearch client."""
    load_dotenv()
    
    host = os.getenv('OPENSEARCH_HOST', 'localhost')
    port = int(os.getenv('OPENSEARCH_PORT', 9200))
    
    return OpenSearch(
        hosts=[{'host': host, 'port': port}],
        http_compress=True,
        use_ssl=False,
        verify_certs=False,
    )


def print_results(response, title):
    """Pretty print search results."""
    print(f"\n{'='*50}")
    print(f"{title}")
    print(f"{'='*50}")
    print(f"Total hits: {response['hits']['total']['value']}")
    
    for i, hit in enumerate(response['hits']['hits'][:5], 1):
        source = hit['_source']
        print(f"\n{i}. {source['product_name']}")
        print(f"   Category: {source['category']}")
        print(f"   Brand: {source.get('brand', 'Unknown')}")
        print(f"   Price: ₹{source['sale_price']} (MRP: ₹{source['mrp']})")
        print(f"   Discount: {source.get('discount_percentage', 0):.1f}%")
        print(f"   Weight: {source['weight']}g")
        print(f"   Source: {source['source']}")


def main():
    """Run example queries."""
    client = create_client()
    index_name = os.getenv('INDEX_NAME', 'grocery-products')
    
    # Test connection
    try:
        info = client.info()
        print(f"Connected to OpenSearch: {info['cluster_name']}")
    except Exception as e:
        print(f"Failed to connect: {e}")
        return
    
    # Example 1: Simple text search
    query1 = {
        "query": {
            "match": {
                "product_name": "tomato"
            }
        },
        "size": 5
    }
    
    response1 = client.search(index=index_name, body=query1)
    print_results(response1, "Example 1: Search for 'tomato' products")
    
    # Example 2: Category filter
    query2 = {
        "query": {
            "bool": {
                "must": [
                    {"term": {"category.keyword": "Vegetables"}}
                ],
                "filter": [
                    {"range": {"sale_price": {"gte": 50, "lte": 100}}}
                ]
            }
        },
        "sort": [{"sale_price": {"order": "asc"}}],
        "size": 5
    }
    
    response2 = client.search(index=index_name, body=query2)
    print_results(response2, "Example 2: Vegetables priced between ₹50-100")
    
    # Example 3: High discount products
    query3 = {
        "query": {
            "range": {
                "discount_percentage": {"gte": 30}
            }
        },
        "sort": [{"discount_percentage": {"order": "desc"}}],
        "size": 5
    }
    
    response3 = client.search(index=index_name, body=query3)
    print_results(response3, "Example 3: Products with 30%+ discount")
    
    # Example 4: Aggregations - Top categories
    agg_query = {
        "size": 0,
        "aggs": {
            "categories": {
                "terms": {
                    "field": "category.keyword",
                    "size": 10
                }
            },
            "avg_price": {
                "avg": {
                    "field": "sale_price"
                }
            },
            "price_ranges": {
                "range": {
                    "field": "sale_price",
                    "ranges": [
                        {"to": 25, "key": "Under ₹25"},
                        {"from": 25, "to": 50, "key": "₹25-50"},
                        {"from": 50, "to": 100, "key": "₹50-100"},
                        {"from": 100, "key": "Above ₹100"}
                    ]
                }
            }
        }
    }
    
    agg_response = client.search(index=index_name, body=agg_query)
    
    print(f"\n{'='*50}")
    print("Example 4: Data Analytics")
    print(f"{'='*50}")
    
    print(f"\nAverage Price: ₹{agg_response['aggregations']['avg_price']['value']:.2f}")
    
    print("\nTop Categories:")
    for bucket in agg_response['aggregations']['categories']['buckets']:
        print(f"  {bucket['key']}: {bucket['doc_count']} products")
    
    print("\nPrice Distribution:")
    for bucket in agg_response['aggregations']['price_ranges']['buckets']:
        print(f"  {bucket['key']}: {bucket['doc_count']} products")
    
    # Example 5: Complex search with multiple conditions
    query5 = {
        "query": {
            "bool": {
                "must": [
                    {
                        "multi_match": {
                            "query": "organic fresh",
                            "fields": ["product_name^2", "brand"],
                            "type": "best_fields"
                        }
                    }
                ],
                "filter": [
                    {"term": {"category.keyword": "Vegetables"}},
                    {"range": {"quantity": {"gt": 0}}}
                ]
            }
        },
        "sort": [
            {"discount_percentage": {"order": "desc"}},
            {"sale_price": {"order": "asc"}}
        ],
        "size": 5
    }
    
    response5 = client.search(index=index_name, body=query5)
    print_results(response5, "Example 5: Organic/Fresh vegetables in stock")


if __name__ == "__main__":
    main()
