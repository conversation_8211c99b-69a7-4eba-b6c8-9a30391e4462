#!/usr/bin/env python3
"""
CSV to OpenSearch Data Ingestion Tool

This script reads CSV data and uploads it to OpenSearch with proper error handling,
data transformation, and bulk indexing for optimal performance.

Usage:
    python csv_to_opensearch.py [--csv-file path/to/file.csv] [--index-name custom-index]

Requirements:
    pip install opensearch-py python-dotenv pandas
"""

import os
import sys
import json
import logging
import argparse
from typing import Dict, List, Any, Optional
from datetime import datetime
import pandas as pd
from dotenv import load_dotenv
from opensearchpy import OpenSearch, helpers
from opensearchpy.exceptions import RequestError, ConnectionError


class OpenSearchIngester:
    """Handles CSV data ingestion into OpenSearch."""

    def __init__(self, host: str = "localhost", port: int = 9200,
                 use_ssl: bool = False, verify_certs: bool = False):
        """Initialize OpenSearch connection."""
        self.client = OpenSearch(
            hosts=[{'host': host, 'port': port}],
            http_compress=True,
            use_ssl=use_ssl,
            verify_certs=verify_certs,
            ssl_assert_hostname=False,
            ssl_show_warn=False,
        )
        self.logger = self._setup_logging()

    def _setup_logging(self) -> logging.Logger:
        """Configure logging for the ingestion process."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('opensearch_ingestion.log')
            ]
        )
        return logging.getLogger(__name__)

    def test_connection(self) -> bool:
        """Test connection to OpenSearch cluster."""
        try:
            info = self.client.info()
            self.logger.info(f"Connected to OpenSearch cluster: {info['cluster_name']}")
            self.logger.info(f"OpenSearch version: {info['version']['number']}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to connect to OpenSearch: {e}")
            return False

    def create_index_mapping(self, index_name: str) -> bool:
        """Create index with optimized mapping for grocery product data."""
        mapping = {
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "analysis": {
                    "analyzer": {
                        "product_analyzer": {
                            "type": "custom",
                            "tokenizer": "standard",
                            "filter": ["lowercase", "stop"]
                        }
                    }
                }
            },
            "mappings": {
                "properties": {
                    "product_name": {
                        "type": "text",
                        "analyzer": "product_analyzer",
                        "fields": {
                            "keyword": {"type": "keyword"}
                        }
                    },
                    "brand": {
                        "type": "keyword"
                    },
                    "category": {
                        "type": "keyword"
                    },
                    "weight": {
                        "type": "integer"
                    },
                    "sale_price": {
                        "type": "float"
                    },
                    "mrp": {
                        "type": "float"
                    },
                    "discount": {
                        "type": "float"
                    },
                    "quantity": {
                        "type": "integer"
                    },
                    "source": {
                        "type": "keyword"
                    },
                    "thumbnail": {
                        "type": "text",
                        "index": False
                    },
                    "image_urls": {
                        "type": "text",
                        "index": False
                    },
                    "discount_percentage": {
                        "type": "float"
                    },
                    "price_per_gram": {
                        "type": "float"
                    },
                    "ingestion_timestamp": {
                        "type": "date"
                    }
                }
            }
        }

        try:
            if self.client.indices.exists(index=index_name):
                self.logger.info(f"Index '{index_name}' already exists")
                return True

            response = self.client.indices.create(index=index_name, body=mapping)
            self.logger.info(f"Created index '{index_name}' successfully")
            return True
        except RequestError as e:
            self.logger.error(f"Failed to create index: {e}")
            return False

    def transform_data(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Transform CSV data for OpenSearch ingestion."""
        self.logger.info(f"Transforming {len(df)} records")

        # Data cleaning and transformation
        df = df.copy()

        # Handle missing values
        df['brand'] = df['brand'].fillna('Unknown')
        df['thumbnail'] = df['thumbnail'].fillna('')
        df['image_urls'] = df['image_urls'].fillna('')

        # Convert numeric columns
        numeric_columns = ['weight', 'sale_price', 'mrp', 'discount', 'quantity']
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)

        # Calculate derived fields
        df['discount_percentage'] = ((df['mrp'] - df['sale_price']) / df['mrp'] * 100).round(2)
        df['price_per_gram'] = (df['sale_price'] / df['weight']).round(4)
        df['ingestion_timestamp'] = datetime.utcnow().isoformat()

        # Convert to list of dictionaries
        records = df.to_dict('records')

        # Clean up any remaining NaN values
        for record in records:
            for key, value in record.items():
                if pd.isna(value):
                    record[key] = None if key in ['brand'] else 0

        self.logger.info(f"Transformation complete. Sample record keys: {list(records[0].keys())}")
        return records

    def bulk_index_data(self, index_name: str, records: List[Dict[str, Any]],
                       batch_size: int = 1000) -> bool:
        """Bulk index data into OpenSearch."""
        self.logger.info(f"Starting bulk indexing of {len(records)} records")

        def generate_docs():
            for i, record in enumerate(records):
                yield {
                    "_index": index_name,
                    "_id": i + 1,  # Simple incremental ID
                    "_source": record
                }

        try:
            # Use helpers.bulk for efficient bulk indexing
            success_count, failed_items = helpers.bulk(
                self.client,
                generate_docs(),
                chunk_size=batch_size,
                request_timeout=60,
                max_retries=3,
                initial_backoff=2,
                max_backoff=600
            )

            self.logger.info(f"Successfully indexed {success_count} documents")

            if failed_items:
                self.logger.warning(f"Failed to index {len(failed_items)} documents")
                for item in failed_items[:5]:  # Log first 5 failures
                    self.logger.error(f"Failed item: {item}")

            return len(failed_items) == 0

        except Exception as e:
            self.logger.error(f"Bulk indexing failed: {e}")
            return False

    def get_index_stats(self, index_name: str) -> Dict[str, Any]:
        """Get statistics about the indexed data."""
        try:
            stats = self.client.indices.stats(index=index_name)
            count_response = self.client.count(index=index_name)

            return {
                "document_count": count_response['count'],
                "index_size": stats['indices'][index_name]['total']['store']['size_in_bytes'],
                "index_size_mb": round(stats['indices'][index_name]['total']['store']['size_in_bytes'] / (1024*1024), 2)
            }
        except Exception as e:
            self.logger.error(f"Failed to get index stats: {e}")
            return {}


def load_csv_data(file_path: str) -> pd.DataFrame:
    """Load and validate CSV data."""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"CSV file not found: {file_path}")

    try:
        # Read CSV with proper encoding handling
        df = pd.read_csv(file_path, encoding='utf-8')
        print(f"Loaded {len(df)} rows from {file_path}")

        # Validate required columns
        required_columns = ['product_name', 'category', 'sale_price', 'mrp']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")

        print(f"CSV columns: {list(df.columns)}")
        print(f"Sample data preview:")
        print(df.head(3).to_string())

        return df

    except Exception as e:
        raise Exception(f"Failed to load CSV data: {e}")


def main():
    """Main function to orchestrate the CSV ingestion process."""
    parser = argparse.ArgumentParser(description='Ingest CSV data into OpenSearch')
    parser.add_argument('--csv-file', default='quick_commerces-Merged_Products.csv',
                       help='Path to CSV file (default: quick_commerces-Merged_Products.csv)')
    parser.add_argument('--index-name', help='OpenSearch index name (overrides .env)')
    parser.add_argument('--batch-size', type=int, help='Batch size for bulk indexing (overrides .env)')
    parser.add_argument('--recreate-index', action='store_true',
                       help='Delete and recreate the index if it exists')

    args = parser.parse_args()

    # Load environment variables
    load_dotenv()

    # Configuration from environment or arguments
    opensearch_host = os.getenv('OPENSEARCH_HOST', 'localhost')
    opensearch_port = int(os.getenv('OPENSEARCH_PORT', 9200))
    index_name = args.index_name or os.getenv('INDEX_NAME', 'grocery-products')
    batch_size = args.batch_size or int(os.getenv('BATCH_SIZE', 1000))

    print(f"Configuration:")
    print(f"  OpenSearch: {opensearch_host}:{opensearch_port}")
    print(f"  Index: {index_name}")
    print(f"  CSV File: {args.csv_file}")
    print(f"  Batch Size: {batch_size}")
    print()

    try:
        # Initialize ingester
        ingester = OpenSearchIngester(host=opensearch_host, port=opensearch_port)

        # Test connection
        if not ingester.test_connection():
            print("❌ Failed to connect to OpenSearch. Please check your configuration.")
            return 1

        # Handle index recreation
        if args.recreate_index and ingester.client.indices.exists(index=index_name):
            print(f"🗑️  Deleting existing index '{index_name}'...")
            ingester.client.indices.delete(index=index_name)

        # Create index with mapping
        if not ingester.create_index_mapping(index_name):
            print("❌ Failed to create index mapping.")
            return 1

        # Load CSV data
        print(f"📁 Loading CSV data from {args.csv_file}...")
        df = load_csv_data(args.csv_file)

        # Transform data
        print("🔄 Transforming data...")
        records = ingester.transform_data(df)

        # Bulk index data
        print(f"📤 Indexing {len(records)} records...")
        if ingester.bulk_index_data(index_name, records, batch_size):
            print("✅ Data ingestion completed successfully!")
        else:
            print("⚠️  Data ingestion completed with some errors.")

        # Show final statistics
        stats = ingester.get_index_stats(index_name)
        if stats:
            print(f"\n📊 Index Statistics:")
            print(f"  Documents: {stats['document_count']:,}")
            print(f"  Index Size: {stats['index_size_mb']} MB")

        return 0

    except Exception as e:
        print(f"❌ Error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
