#!/usr/bin/env python3
"""
Test script to validate OpenSearch setup and data ingestion.

This script performs basic validation of:
1. OpenSearch connectivity
2. CSV file availability
3. Python dependencies
4. Index creation and data ingestion (small sample)

Usage:
    python test_setup.py
"""

import os
import sys
import pandas as pd
from dotenv import load_dotenv

def test_dependencies():
    """Test if required Python packages are installed."""
    print("🔍 Testing Python dependencies...")
    
    try:
        import opensearchpy
        import pandas
        import dotenv
        print("✅ All required packages are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Run: pip install -r requirements.txt")
        return False

def test_env_file():
    """Test if .env file exists and has required variables."""
    print("🔍 Testing environment configuration...")
    
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        return False
    
    load_dotenv()
    
    required_vars = ['CLUSTER_NAME', 'NODE_NAME', 'OPENSEARCH_PORT', 'INDEX_NAME']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        return False
    
    print("✅ Environment configuration is valid")
    print(f"   Cluster: {os.getenv('CLUSTER_NAME')}")
    print(f"   Node: {os.getenv('NODE_NAME')}")
    print(f"   Port: {os.getenv('OPENSEARCH_PORT')}")
    print(f"   Index: {os.getenv('INDEX_NAME')}")
    return True

def test_csv_file():
    """Test if CSV file exists and is readable."""
    print("🔍 Testing CSV file...")
    
    csv_file = 'quick_commerces-Merged_Products.csv'
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file not found: {csv_file}")
        return False
    
    try:
        df = pd.read_csv(csv_file, nrows=5)  # Read just first 5 rows
        print(f"✅ CSV file is readable")
        print(f"   Columns: {list(df.columns)}")
        print(f"   Sample rows: {len(df)}")
        return True
    except Exception as e:
        print(f"❌ Failed to read CSV file: {e}")
        return False

def test_opensearch_connection():
    """Test connection to OpenSearch."""
    print("🔍 Testing OpenSearch connection...")
    
    try:
        from opensearchpy import OpenSearch
        
        load_dotenv()
        host = os.getenv('OPENSEARCH_HOST', 'localhost')
        port = int(os.getenv('OPENSEARCH_PORT', 9200))
        
        client = OpenSearch(
            hosts=[{'host': host, 'port': port}],
            http_compress=True,
            use_ssl=False,
            verify_certs=False,
        )
        
        info = client.info()
        print("✅ OpenSearch connection successful")
        print(f"   Cluster: {info['cluster_name']}")
        print(f"   Version: {info['version']['number']}")
        return True
        
    except Exception as e:
        print(f"❌ OpenSearch connection failed: {e}")
        print("   Make sure OpenSearch is running: make up")
        return False

def test_ingestion_script():
    """Test if the ingestion script can be imported."""
    print("🔍 Testing ingestion script...")
    
    try:
        # Try to import the main components
        sys.path.append('.')
        from csv_to_opensearch import OpenSearchIngester, load_csv_data
        print("✅ Ingestion script is importable")
        return True
    except Exception as e:
        print(f"❌ Failed to import ingestion script: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 OpenSearch Setup Validation")
    print("=" * 50)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Environment", test_env_file),
        ("CSV File", test_csv_file),
        ("Ingestion Script", test_ingestion_script),
        ("OpenSearch Connection", test_opensearch_connection),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(tests)}")
    
    if passed == len(tests):
        print("\n🎉 All tests passed! You're ready to ingest data.")
        print("\nNext steps:")
        print("1. make up          # Start OpenSearch")
        print("2. make ingest-data # Load CSV data")
        print("3. make query-examples # Test queries")
    else:
        print("\n⚠️  Some tests failed. Please fix the issues above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
