#!/bin/bash

# Script to verify OpenSearch cluster configuration
# This script checks if the cluster and node names match your .env settings

echo "🔍 OpenSearch Configuration Verification"
echo "========================================"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found!"
    exit 1
fi

# Extract values from .env file (avoiding sourcing due to JAVA_OPTS)
CLUSTER_NAME=$(grep "^CLUSTER_NAME=" .env | cut -d'=' -f2)
NODE_NAME=$(grep "^NODE_NAME=" .env | cut -d'=' -f2)
OPENSEARCH_PORT=$(grep "^OPENSEARCH_PORT=" .env | cut -d'=' -f2)
DASHBOARDS_PORT=$(grep "^DASHBOARDS_PORT=" .env | cut -d'=' -f2)

echo "📋 Expected Configuration from .env:"
echo "  CLUSTER_NAME: ${CLUSTER_NAME}"
echo "  NODE_NAME: ${NODE_NAME}"
echo "  OPENSEARCH_PORT: ${OPENSEARCH_PORT}"
echo ""

# Check if OpenSearch is running
echo "🔍 Checking OpenSearch Status:"
if ! curl -s "http://localhost:${OPENSEARCH_PORT}/" > /dev/null 2>&1; then
    echo "❌ OpenSearch is not responding on port ${OPENSEARCH_PORT}"
    echo "   Please start OpenSearch: docker compose up -d"
    exit 1
fi

# Get current configuration
CLUSTER_INFO=$(curl -s "http://localhost:${OPENSEARCH_PORT}/")
CURRENT_CLUSTER=$(echo "$CLUSTER_INFO" | grep -o '"cluster_name":"[^"]*"' | cut -d'"' -f4)
CURRENT_NODE=$(echo "$CLUSTER_INFO" | grep -o '"name":"[^"]*"' | cut -d'"' -f4)

echo "📊 Current Running Configuration:"
echo "  Cluster Name: ${CURRENT_CLUSTER}"
echo "  Node Name: ${CURRENT_NODE}"
echo ""

# Verify configuration matches
if [ "$CURRENT_CLUSTER" = "$CLUSTER_NAME" ] && [ "$CURRENT_NODE" = "$NODE_NAME" ]; then
    echo "✅ Configuration Verification: SUCCESS"
    echo "   ✅ Cluster name matches: ${CLUSTER_NAME}"
    echo "   ✅ Node name matches: ${NODE_NAME}"

    # Additional health checks
    echo ""
    echo "🏥 Cluster Health Check:"
    HEALTH=$(curl -s "http://localhost:${OPENSEARCH_PORT}/_cluster/health")
    STATUS=$(echo "$HEALTH" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    NODES=$(echo "$HEALTH" | grep -o '"number_of_nodes":[0-9]*' | cut -d':' -f2)

    echo "   Status: ${STATUS}"
    echo "   Nodes: ${NODES}"

    if [ "$STATUS" = "green" ]; then
        echo "   ✅ Cluster is healthy"
    elif [ "$STATUS" = "yellow" ]; then
        echo "   ⚠️  Cluster is functional but has warnings"
    else
        echo "   ❌ Cluster has issues"
    fi

    echo ""
    echo "🌐 Access Points:"
    echo "   OpenSearch API: http://localhost:${OPENSEARCH_PORT}"
    echo "   OpenSearch Dashboards: http://localhost:${DASHBOARDS_PORT:-5601}"

    echo ""
    echo "🎉 Your OpenSearch cluster is properly configured!"

else
    echo "❌ Configuration Verification: FAILED"
    echo "   Expected: ${CLUSTER_NAME}/${NODE_NAME}"
    echo "   Current:  ${CURRENT_CLUSTER}/${CURRENT_NODE}"
    echo ""
    echo "🔧 To fix this issue:"
    echo "   1. Update your .env file with the correct values"
    echo "   2. Run: docker compose down && docker compose up -d"
    echo "   3. Wait for services to start and run this script again"
    exit 1
fi

echo ""
echo "📋 Quick Verification Commands:"
echo "   curl http://localhost:${OPENSEARCH_PORT}/ | jq"
echo "   curl http://localhost:${OPENSEARCH_PORT}/_cluster/health | jq"
echo "   curl http://localhost:${OPENSEARCH_PORT}/_cat/nodes?v"
