# OpenSearch Docker Development Setup

This Docker Compose configuration provides a complete OpenSearch development environment with OpenSearch and OpenSearch Dashboards.

## Quick Start

1. **Start the services:**
   ```bash
   docker compose up -d
   ```

2. **Check service status:**
   ```bash
   docker compose ps
   ```

3. **Access OpenSearch:**
   - REST API: http://localhost:9200
   - Health check: http://localhost:9200/_cluster/health

4. **Access OpenSearch Dashboards:**
   - Web UI: http://localhost:5601

5. **Stop the services:**
   ```bash
   docker compose down
   ```

## Configuration

### Environment Variables

The setup uses a `.env` file for easy configuration. Key variables:

- `OPENSEARCH_PORT`: OpenSearch REST API port (default: 9200)
- `DASHBOARDS_PORT`: Dashboards web UI port (default: 5601)
- `OPENSEARCH_JAVA_OPTS`: JVM memory settings (default: -Xms1g -Xmx1g)
- `DISABLE_SECURITY`: Disable security plugins for development (default: true)

### Memory Requirements

- **Minimum**: 2GB RAM available to Docker
- **Recommended**: 4GB+ RAM for optimal performance
- Adjust `OPENSEARCH_JAVA_OPTS` in `.env` based on your system

### Data Persistence

Data is persisted in a Docker volume named `opensearch-data`. To start fresh:

```bash
docker compose down -v  # Removes volumes
docker compose up -d
```

## Development Usage

### Testing API Endpoints

```bash
# Cluster health
curl http://localhost:9200/_cluster/health

# List indices
curl http://localhost:9200/_cat/indices?v

# Create a test index
curl -X PUT http://localhost:9200/test-index

# Add a document
curl -X POST http://localhost:9200/test-index/_doc \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello OpenSearch!"}'

# Search documents
curl http://localhost:9200/test-index/_search
```

### Using with Application Development

The OpenSearch instance is accessible at `localhost:9200` from your host machine. Configure your applications to connect to this endpoint.

Example connection strings:
- **Python**: `http://localhost:9200`
- **Node.js**: `http://localhost:9200`
- **Java**: `http://localhost:9200`

## Troubleshooting

### Common Issues

1. **Port conflicts**: Change ports in `.env` if 9200 or 5601 are in use
2. **Memory issues**: Reduce `OPENSEARCH_JAVA_OPTS` memory allocation
3. **Permission errors**: Ensure Docker has sufficient permissions

### Logs

View service logs:
```bash
# All services
docker-compose logs

# OpenSearch only
docker-compose logs opensearch

# Follow logs in real-time
docker-compose logs -f opensearch
```

### Health Checks

Check if services are healthy:
```bash
docker-compose ps
```

Healthy services will show "Up (healthy)" status.

## Security Notice

⚠️ **WARNING**: This configuration disables security plugins for development convenience.

**DO NOT USE IN PRODUCTION!**

For production deployments:
1. Enable security plugins
2. Configure proper authentication
3. Set up TLS/SSL certificates
4. Review and harden all security settings

## Customization

### Custom Configuration Files

Uncomment volume mounts in `docker-compose.yml` to use custom config files:

```yaml
volumes:
  - ./config/opensearch.yml:/usr/share/opensearch/config/opensearch.yml:ro
  - ./config/opensearch_dashboards.yml:/usr/share/opensearch-dashboards/config/opensearch_dashboards.yml:ro
```

### Adding Plugins

Mount a plugins directory:

```yaml
volumes:
  - ./plugins:/usr/share/opensearch/plugins
```

### Resource Limits

Adjust resource limits in `docker-compose.yml` based on your system:

```yaml
deploy:
  resources:
    limits:
      memory: 4g  # Increase for better performance
    reservations:
      memory: 2g
```

## Useful Commands

```bash
# Start services
docker-compose up -d

# Stop services (keep data)
docker-compose down

# Stop services and remove data
docker-compose down -v

# Restart a specific service
docker-compose restart opensearch

# View resource usage
docker stats

# Execute commands in OpenSearch container
docker-compose exec opensearch bash

# Update to latest images
docker-compose pull
docker-compose up -d
```
