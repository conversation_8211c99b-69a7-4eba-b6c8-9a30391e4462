# OpenSearch Docker Development Setup

Complete OpenSearch development environment with OpenSearch and OpenSearch Dashboards.

## Quick Start

```bash
# Start services
make up

# Check status
make status

# Access points
# - OpenSearch API: http://localhost:9200
# - Dashboards: http://localhost:5601

# Stop services
make down
```

## Configuration

Key `.env` variables:
- `CLUSTER_NAME`: Cluster name (default: grocery-cluster)
- `NODE_NAME`: Node name (default: grocery-dev)
- `OPENSEARCH_PORT`: API port (default: 9200)
- `DASHBOARDS_PORT`: UI port (default: 5601)

**Memory**: Minimum 2GB RAM, recommended 4GB+

**Fresh start**: `make clean` removes all data

## Available Commands

```bash
make help          # Show all commands
make up            # Start services
make down          # Stop services
make restart       # Restart services
make status        # Show service status
make health        # Check cluster health
make test          # Test connectivity
make verify-config # Verify configuration
make apply-config  # Apply .env changes
make clean         # Remove all data
```

## API Testing

```bash
# Cluster health
curl http://localhost:9200/_cluster/health

# Create index and add document
curl -X PUT http://localhost:9200/test-index
curl -X POST http://localhost:9200/test-index/_doc \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello OpenSearch!"}'
```

## Troubleshooting

**Common Issues**:
- Port conflicts: Change ports in `.env`
- Memory issues: Reduce `OPENSEARCH_JAVA_OPTS` in `.env`
- Service issues: Check `make logs` or `make status`

**View logs**: `make logs` or `docker compose logs opensearch`

## Security Notice

⚠️ **Development only** - Security disabled for convenience. **DO NOT USE IN PRODUCTION!**
