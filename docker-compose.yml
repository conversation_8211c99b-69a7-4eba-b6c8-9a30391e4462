services:
  opensearch:
    image: opensearchproject/opensearch:${OPENSEARCH_VERSION:-2.11.1}
    container_name: opensearch-dev
    environment:
      # Cluster configuration for single-node development
      - cluster.name=${CLUSTER_NAME:-opensearch-cluster}
      - node.name=${NODE_NAME:-opensearch-dev}
      - discovery.type=single-node

      # Disable security plugins for local development
      # WARNING: Do not use these settings in production!
      - plugins.security.disabled=${DISABLE_SECURITY:-true}
      - DISABLE_INSTALL_DEMO_CONFIG=true

      # Memory settings - adjust based on your system
      - "OPENSEARCH_JAVA_OPTS=${OPENSEARCH_JAVA_OPTS:--Xms1g -Xmx1g}"

      # Network settings
      - network.host=0.0.0.0
      - http.port=9200
      - transport.tcp.port=9300

      # Performance settings for development
      - bootstrap.memory_lock=true
      - cluster.routing.allocation.disk.threshold_enabled=false

      # Logging level (change to DEBUG for more verbose logs)
      - logger.level=${LOG_LEVEL:-INFO}

    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536

    ports:
      - "${OPENSEARCH_PORT:-9200}:9200"  # REST API
      - "${OPENSEARCH_TRANSPORT_PORT:-9300}:9300"  # Inter-node communication

    volumes:
      # Persist OpenSearch data
      - opensearch-data:/usr/share/opensearch/data

      # Optional: Mount custom configuration
      # - ./config/opensearch.yml:/usr/share/opensearch/config/opensearch.yml:ro

      # Optional: Mount custom plugins
      # - ./plugins:/usr/share/opensearch/plugins

    networks:
      - opensearch-net

    # Resource limits - adjust based on your system capabilities
    deploy:
      resources:
        limits:
          memory: 2g
        reservations:
          memory: 1g

    # Health check to ensure OpenSearch is ready
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  opensearch-dashboards:
    image: opensearchproject/opensearch-dashboards:${OPENSEARCH_DASHBOARDS_VERSION:-2.11.1}
    container_name: opensearch-dashboards-dev
    environment:
      # Connect to OpenSearch
      - OPENSEARCH_HOSTS=http://opensearch:9200

      # Disable security for local development
      # WARNING: Do not use these settings in production!
      - DISABLE_SECURITY_DASHBOARDS_PLUGIN=${DISABLE_SECURITY:-true}

      # Server configuration
      - server.host=0.0.0.0
      - server.port=5601

      # Optional: Set a custom base path
      # - server.basePath=/dashboards

    ports:
      - "${DASHBOARDS_PORT:-5601}:5601"

    # volumes:
      # Optional: Mount custom dashboard configuration
      # - ./config/opensearch_dashboards.yml:/usr/share/opensearch-dashboards/config/opensearch_dashboards.yml:ro

    networks:
      - opensearch-net

    depends_on:
      opensearch:
        condition: service_healthy

    # Resource limits
    deploy:
      resources:
        limits:
          memory: 1g
        reservations:
          memory: 512m

    # Health check for Dashboards
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

# Named volumes for data persistence
volumes:
  opensearch-data:
    driver: local

# Custom network for service communication
networks:
  opensearch-net:
    driver: bridge

# Optional: Add a development utilities container
# Uncomment the section below if you want additional development tools
#
# opensearch-utils:
#   image: curlimages/curl:latest
#   container_name: opensearch-utils
#   networks:
#     - opensearch-net
#   depends_on:
#     - opensearch
#   command: tail -f /dev/null  # Keep container running
#   # This container can be used for testing API calls:
#   # docker-compose exec opensearch-utils curl http://opensearch:9200/_cluster/health
