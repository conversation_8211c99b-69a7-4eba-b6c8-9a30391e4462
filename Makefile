# OpenSearch Docker Development Makefile

.PHONY: help up down restart logs status clean test health verify-config apply-config

# Default target
help:
	@echo "OpenSearch Docker Development Commands:"
	@echo ""
	@echo "  make up        - Start OpenSearch and Dashboards"
	@echo "  make down      - Stop services (keep data)"
	@echo "  make restart   - Restart all services"
	@echo "  make logs      - Show logs from all services"
	@echo "  make status    - Show service status"
	@echo "  make clean     - Stop services and remove data"
	@echo "  make test      - Test OpenSearch connectivity"
	@echo "  make health    - Check service health"
	@echo "  make verify-config - Verify cluster and node names"
	@echo "  make apply-config  - Apply .env changes (restart services)"
	@echo ""

# Start services
up:
	@echo "Starting OpenSearch development environment..."
	docker compose up -d
	@echo "Services started. OpenSearch: http://localhost:9200, Dashboards: http://localhost:5601"

# Stop services (keep data)
down:
	@echo "Stopping OpenSearch services..."
	docker compose down

# Restart services
restart:
	@echo "Restarting OpenSearch services..."
	docker compose restart

# Show logs
logs:
	docker compose logs -f

# Show service status
status:
	docker compose ps

# Stop services and remove data
clean:
	@echo "Stopping services and removing data..."
	docker compose down -v
	@echo "All data removed. Next 'make up' will start fresh."

# Test OpenSearch connectivity
test:
	@echo "Testing OpenSearch connectivity..."
	@curl -s http://localhost:9200/_cluster/health | jq . || echo "OpenSearch not responding or jq not installed"
	@echo ""
	@curl -s http://localhost:9200/_cat/nodes?v || echo "OpenSearch not responding"

# Check service health
health:
	@echo "Checking service health..."
	@docker compose ps
	@echo ""
	@echo "OpenSearch health:"
	@curl -s http://localhost:9200/_cluster/health | jq '.status' || echo "Not responding"
	@echo ""
	@echo "Dashboards health:"
	@curl -s -o /dev/null -w "%{http_code}" http://localhost:5601/api/status || echo "Not responding"
	@echo ""

# Verify cluster and node configuration
verify-config:
	@echo "=== Configuration Verification ==="
	@echo "Expected: $$(grep CLUSTER_NAME .env | cut -d= -f2)/$$(grep NODE_NAME .env | cut -d= -f2)"
	@echo "Current:  $$(curl -s http://localhost:9200/ | jq -r '.cluster_name + "/" + .name')"

# Apply configuration changes (restart with new .env values)
apply-config:
	@echo "Applying configuration changes..."
	@echo "Stopping services..."
	@docker compose down
	@echo "Starting with updated configuration..."
	@docker compose up -d
	@echo "Waiting for services..."
	@sleep 10
	@make verify-config
