# OpenSearch Docker Development Makefile

.PHONY: help up down restart logs status clean test health

# Default target
help:
	@echo "OpenSearch Docker Development Commands:"
	@echo ""
	@echo "  make up        - Start OpenSearch and Dashboards"
	@echo "  make down      - Stop services (keep data)"
	@echo "  make restart   - Restart all services"
	@echo "  make logs      - Show logs from all services"
	@echo "  make status    - Show service status"
	@echo "  make clean     - Stop services and remove data"
	@echo "  make test      - Test OpenSearch connectivity"
	@echo "  make health    - Check service health"
	@echo ""

# Start services
up:
	@echo "Starting OpenSearch development environment..."
	docker compose up -d
	@echo "Services started. OpenSearch: http://localhost:9200, Dashboards: http://localhost:5601"

# Stop services (keep data)
down:
	@echo "Stopping OpenSearch services..."
	docker compose down

# Restart services
restart:
	@echo "Restarting OpenSearch services..."
	docker compose restart

# Show logs
logs:
	docker compose logs -f

# Show service status
status:
	docker compose ps

# Stop services and remove data
clean:
	@echo "Stopping services and removing data..."
	docker compose down -v
	@echo "All data removed. Next 'make up' will start fresh."

# Test OpenSearch connectivity
test:
	@echo "Testing OpenSearch connectivity..."
	@curl -s http://localhost:9200/_cluster/health | jq . || echo "OpenSearch not responding or jq not installed"
	@echo ""
	@curl -s http://localhost:9200/_cat/nodes?v || echo "OpenSearch not responding"

# Check service health
health:
	@echo "Checking service health..."
	@docker compose ps
	@echo ""
	@echo "OpenSearch health:"
	@curl -s http://localhost:9200/_cluster/health | jq '.status' || echo "Not responding"
	@echo ""
	@echo "Dashboards health:"
	@curl -s -o /dev/null -w "%{http_code}" http://localhost:5601/api/status || echo "Not responding"
	@echo ""
